<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>

<VBox spacing="20.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.FormController">
   <children>
      <!-- 表单标题 -->
      <Label fx:id="formTitleLabel" style="-fx-font-size: 18px; -fx-font-weight: bold;" text="数据采集表单" />
      
      <!-- 表单内容 -->
      <GridPane hgap="10.0" vgap="15.0">
        <columnConstraints>
          <javafx.scene.layout.ColumnConstraints hgrow="NEVER" minWidth="100.0" />
          <javafx.scene.layout.ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
            <javafx.scene.layout.ColumnConstraints hgrow="NEVER" minWidth="100.0" />
            <javafx.scene.layout.ColumnConstraints hgrow="ALWAYS" minWidth="200.0" />
        </columnConstraints>
        <rowConstraints>
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
          <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
            <javafx.scene.layout.RowConstraints minHeight="30.0" vgrow="SOMETIMES" />
            <javafx.scene.layout.RowConstraints minHeight="80.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <!-- 基本信息 -->
            <Label text="设备编号:" />
            <TextField fx:id="deviceIdField" promptText="请输入设备编号" GridPane.columnIndex="1" />
            <Label text="操作员:" GridPane.columnIndex="2" />
            <TextField fx:id="operatorField" promptText="请输入操作员姓名" GridPane.columnIndex="3" />
            
            <!-- 传感器数据 -->
            <Label text="温度(°C):" GridPane.rowIndex="1" />
            <TextField fx:id="temperatureField" promptText="请输入温度值" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <Label text="湿度(%):" GridPane.columnIndex="2" GridPane.rowIndex="1" />
            <TextField fx:id="humidityField" promptText="请输入湿度值" GridPane.columnIndex="3" GridPane.rowIndex="1" />
            
            <Label text="压力(Pa):" GridPane.rowIndex="2" />
            <TextField fx:id="pressureField" promptText="请输入压力值" GridPane.columnIndex="1" GridPane.rowIndex="2" />
            
            <!-- 备注信息 -->
            <Label text="备注:" GridPane.rowIndex="4" />
            <TextArea fx:id="remarksArea" prefRowCount="3" promptText="请输入备注信息" wrapText="true" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="4" />
         </children>
      </GridPane>
      
      <!-- 状态区域 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <ProgressIndicator fx:id="progressIndicator" prefHeight="20.0" prefWidth="20.0" />
            <Label fx:id="statusLabel" text="" />
         </children>
      </HBox>
      
      <!-- 按钮区域 -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0">
         <children>
            <Button fx:id="submitButton" disable="true" mnemonicParsing="false" text="提交" />
            <Button fx:id="backButton" mnemonicParsing="false" text="返回" />
         </children>
      </HBox>
   </children>
   <padding>
      <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
   </padding>
</VBox>
