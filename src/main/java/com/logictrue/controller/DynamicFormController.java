package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.FormField;
import com.logictrue.service.NetworkService;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 动态表单控制器
 */
public class DynamicFormController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(DynamicFormController.class);
    
    @FXML
    private Label formTitleLabel;
    
    @FXML
    private ScrollPane formScrollPane;
    
    @FXML
    private VBox formContainer;
    
    @FXML
    private Button submitButton;
    
    @FXML
    private Button backButton;
    
    @FXML
    private ProgressIndicator progressIndicator;
    
    @FXML
    private Label statusLabel;
    
    private ConfigManager configManager;
    private NetworkService networkService;
    private MainController mainController;
    private Map<String, Control> fieldControls = new HashMap<>();
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        networkService = new NetworkService();
        
        // 初始化界面
        initializeUI();
        
        // 加载表单配置
        loadFormConfig();
        
        // 生成动态表单
        generateDynamicForm();
        
        logger.info("动态表单界面初始化完成");
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);
        
        // 绑定事件
        submitButton.setOnAction(event -> submitForm());
        backButton.setOnAction(event -> closeWindow());
        
        // 设置表单容器
        formContainer.setSpacing(15);
        formContainer.setPadding(new Insets(20));
    }
    
    /**
     * 加载表单配置
     */
    private void loadFormConfig() {
        // 设置表单标题
        formTitleLabel.setText(configManager.getFormName());
    }
    
    /**
     * 生成动态表单
     */
    private void generateDynamicForm() {
        List<FormField> formFields = configManager.getFormFields();

        GridPane formGrid = new GridPane();
        formGrid.setHgap(15);
        formGrid.setVgap(15);

        int row = 0;
        int col = 0;
        final int maxColumns = 2; // 每行最多2个字段

        for (FormField field : formFields) {
            // 创建标签
            Label label = new Label(field.getLabel() + ":");
            label.setMinWidth(100);

            // 创建输入控件
            Control inputControl = createInputControl(field);
            fieldControls.put(field.getName(), inputControl);

            // 添加到网格
            formGrid.add(label, col * 2, row);
            formGrid.add(inputControl, col * 2 + 1, row);

            // 更新位置
            col++;
            if (col >= maxColumns || field.getType() == FormField.FieldType.TEXTAREA) {
                col = 0;
                row++;

                // 如果是文本区域，占满整行
                if (field.getType() == FormField.FieldType.TEXTAREA) {
                    GridPane.setColumnSpan(label, 1);
                    GridPane.setColumnSpan(inputControl, maxColumns * 2 - 1);
                }
            }
        }

        formContainer.getChildren().clear();
        formContainer.getChildren().add(formGrid);

        // 更新提交按钮状态
        updateSubmitButtonState();
    }
    
    /**
     * 创建输入控件
     */
    private Control createInputControl(FormField field) {
        Control control;
        
        switch (field.getType()) {
            case TEXTAREA:
                TextArea textArea = new TextArea();
                textArea.setPromptText("请输入" + field.getLabel());
                textArea.setPrefRowCount(3);
                textArea.setWrapText(true);
                control = textArea;
                break;

            case NUMBER:
                TextField numberField = new TextField();
                numberField.setPromptText("请输入" + field.getLabel());
                // 添加数字验证
                numberField.textProperty().addListener((observable, oldValue, newValue) -> {
                    if (!newValue.matches("\\d*\\.?\\d*")) {
                        numberField.setText(oldValue);
                    }
                });
                control = numberField;
                break;

            case DATE:
                DatePicker datePicker = new DatePicker();
                datePicker.setPromptText("请选择" + field.getLabel());
                control = datePicker;
                break;

            case TEXT:
            default:
                TextField textField = new TextField();
                textField.setPromptText("请输入" + field.getLabel());
                control = textField;
                break;
        }
        
        return control;
    }
    
    /**
     * 更新提交按钮状态
     */
    private void updateSubmitButtonState() {
        // 简化逻辑，只要有字段就可以提交
        submitButton.setDisable(false);
    }
    
    /**
     * 获取控件值
     */
    private String getControlValue(Control control) {
        if (control instanceof TextField) {
            return ((TextField) control).getText();
        } else if (control instanceof TextArea) {
            return ((TextArea) control).getText();
        } else if (control instanceof DatePicker) {
            return ((DatePicker) control).getValue() != null ? 
                   ((DatePicker) control).getValue().toString() : null;
        }
        return null;
    }
    
    /**
     * 提交表单
     */
    @FXML
    private void submitForm() {
        // 验证表单数据
        if (!validateForm()) {
            return;
        }
        
        // 收集表单数据
        Map<String, Object> formData = collectFormData();
        
        // 显示进度指示器
        progressIndicator.setVisible(true);
        submitButton.setDisable(true);
        showStatus("正在提交表单...", true);
        
        Task<Boolean> submitTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                return networkService.submitForm(formData).get();
            }
        };
        
        submitTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                updateSubmitButtonState();
                
                boolean success = submitTask.getValue();
                if (success) {
                    showStatus("表单提交成功", true);
                    logger.info("动态表单提交成功: {}", formData);
                    
                    // 延迟关闭窗口
                    Platform.runLater(() -> {
                        try {
                            Thread.sleep(1500);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        closeWindow();
                    });
                } else {
                    showStatus("表单提交失败", false);
                }
            });
        });
        
        submitTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                updateSubmitButtonState();
                showStatus("表单提交异常: " + submitTask.getException().getMessage(), false);
            });
        });
        
        Thread submitThread = new Thread(submitTask);
        submitThread.setDaemon(true);
        submitThread.start();
    }
    
    /**
     * 验证表单数据
     */
    private boolean validateForm() {
        // 简化验证，总是返回true
        return true;
    }
    
    /**
     * 收集表单数据
     */
    private Map<String, Object> collectFormData() {
        Map<String, Object> formData = new HashMap<>();
        
        List<FormField> formFields = configManager.getFormFields();
        for (FormField field : formFields) {
            Control control = fieldControls.get(field.getName());
            String value = getControlValue(control);
            
            if (value != null && !value.trim().isEmpty()) {
                // 根据字段类型转换数据
                switch (field.getType()) {
                    case NUMBER:
                        try {
                            if (value.contains(".")) {
                                formData.put(field.getName(), Double.parseDouble(value));
                            } else {
                                formData.put(field.getName(), Integer.parseInt(value));
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("数字格式错误: {} = {}", field.getName(), value);
                            formData.put(field.getName(), value);
                        }
                        break;
                    default:
                        formData.put(field.getName(), value);
                        break;
                }
            }
        }
        
        // 添加提交时间
        formData.put("submitTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        return formData;
    }
    
    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        Stage stage = (Stage) backButton.getScene().getWindow();
        stage.close();
    }
    
    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }
    
    /**
     * 设置主控制器引用
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }
}
