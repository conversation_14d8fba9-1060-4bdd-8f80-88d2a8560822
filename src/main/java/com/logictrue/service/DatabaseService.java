package com.logictrue.service;

import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.util.JsonParseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 重构后的数据库服务类
 * 完全使用MyBatis Plus进行SQLite数据库操作
 */
public class DatabaseService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);

    private static final String DB_NAME = "data_iot.db";

    private static DatabaseService instance;
    private String dbPath;
    private MyBatisPlusConfig myBatisPlusConfig;
    private DeviceDetectionDataService dataService;
    private DatabaseInitializationService initService;

    private DatabaseService() {
        // 获取jar包同级目录
        String jarDir = getJarDirectory();
        this.dbPath = jarDir + File.separator + DB_NAME;

        logger.info("数据库服务基础初始化完成，数据库路径: {}", dbPath);
    }

    /**
     * 延迟初始化其他组件
     */
    public void initializeComponents() {
        if (this.myBatisPlusConfig == null) {
            // 初始化数据库表结构
            this.initService = DatabaseInitializationService.getInstance();
            this.myBatisPlusConfig = initService.getMyBatisPlusConfig();

            // 初始化数据服务
            this.dataService = new DeviceDetectionDataService();

            logger.info("数据库服务组件初始化完成");
        }
    }

    /**
     * 确保组件已初始化
     */
    private void ensureInitialized() {
        if (this.dataService == null) {
            synchronized (this) {
                if (this.dataService == null) {
                    initializeComponents();
                }
            }
        }
    }

    public void initDatabase() {
        if (this.initService == null) {
            // 初始化数据库表结构
            this.initService = DatabaseInitializationService.getInstance();
            logger.info("数据库表结构初始化完成");
        }
    }

    /**
     * 获取单例实例
     */
    public static synchronized DatabaseService getInstance() {
        if (instance == null) {
            instance = new DatabaseService();
        }
        return instance;
    }

    /**
     * 获取jar包所在目录
     */
    private String getJarDirectory() {
        try {
            // 优先使用系统属性中设置的数据目录
            String dataDir = System.getProperty("iot.data.dir");
            if (dataDir != null && !dataDir.isEmpty()) {
                return dataDir;
            }

            // 如果系统属性未设置，使用原有逻辑
            String jarPath = DatabaseService.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            if (jarFile.isFile()) {
                // 运行的是jar包
                return jarFile.getParent();
            } else {
                // 开发环境，使用项目根目录
                return System.getProperty("user.dir");
            }
        } catch (Exception e) {
            logger.error("获取jar包目录失败", e);
            return System.getProperty("user.dir");
        }
    }

    /**
     * 获取数据库文件路径
     */
    public String getDbPath() {
        return dbPath;
    }

    /**
     * 分页查询设备检测数据记录
     */
    public List<DeviceDetectionData> getExcelDataRecords(int page, int pageSize) {
        try {
            // 确保组件已初始化
            ensureInitialized();

            var deviceDataList = dataService.getPageData(page, pageSize);

            logger.debug("使用MyBatis Plus查询设备检测数据记录成功，页码: {}, 页大小: {}, 记录数: {}",
                    page, pageSize, deviceDataList != null ? deviceDataList.size() : 0);
            return deviceDataList != null ? deviceDataList : new ArrayList<>();

        } catch (Exception e) {
            logger.error("使用MyBatis Plus查询设备检测数据记录失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取Excel数据记录总数
     */
    public int getExcelDataRecordCount() {
        try {
            // 确保组件已初始化
            ensureInitialized();

            long count = dataService.getTotalCount();
            return (int) count;
        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取Excel数据记录总数失败", e);
            return 0;
        }
    }

    /**
     * 根据ID获取设备检测数据详情
     */
    public DeviceDetectionDataDetail getExcelDataDetail(Long detectionDataId) {
        try {
            // 确保组件已初始化
            ensureInitialized();

            var deviceData = dataService.getById(detectionDataId);
            if (deviceData == null) {
                logger.warn("未找到ID为{}的设备检测数据记录", detectionDataId);
                return null;
            }

            DeviceDetectionDataDetail detail = new DeviceDetectionDataDetail();
            detail.setMainRecord(deviceData);

            // 查询基础字段数据
            List<BasicFieldData> basicFields = loadBasicFieldsData(detectionDataId);
            detail.setBasicFields(basicFields);

            // 查询表格数据
            List<TableDataInfo> tableData = loadTableData(detectionDataId);
            detail.setTableData(tableData);

            logger.info("使用MyBatis Plus获取设备检测数据详情成功，ID: {}, 基础字段数: {}, 表格数据数: {}",
                       detectionDataId, basicFields.size(), tableData.size());
            return detail;

        } catch (Exception e) {
            logger.error("使用MyBatis Plus获取设备检测数据详情失败，ID: {}", detectionDataId, e);
            return null;
        }
    }

    /**
     * 加载基础字段数据
     */
    private List<BasicFieldData> loadBasicFieldsData(Long detectionDataId) {
        try {
            // 确保组件已初始化
            ensureInitialized();

            // 查询基础字段数据
            List<DeviceDetectionBasicField> basicFields = dataService.getBasicFieldsByDetectionDataId(detectionDataId);
            List<BasicFieldData> result = new ArrayList<>();

            if (basicFields != null) {
                for (DeviceDetectionBasicField field : basicFields) {
                    BasicFieldData basicFieldData = new BasicFieldData();
                    basicFieldData.setSheetId(field.getSheetId());
                    basicFieldData.setSheetName(field.getSheetName());
                    basicFieldData.setFieldCode(field.getFieldCode());
                    basicFieldData.setFieldName(field.getFieldName());
                    basicFieldData.setFieldValue(field.getFieldValue());
                    basicFieldData.setFieldType(field.getFieldType());
                    basicFieldData.setRowIndex(field.getLabelRowIndex());
                    basicFieldData.setColIndex(field.getLabelColIndex());
                    result.add(basicFieldData);
                }
            }

            logger.debug("加载基础字段数据成功，检测数据ID: {}, 字段数: {}", detectionDataId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("加载基础字段数据失败，检测数据ID: {}", detectionDataId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 加载表格数据
     */
    private List<TableDataInfo> loadTableData(Long detectionDataId) {
        try {
            // 确保组件已初始化
            ensureInitialized();

            // 查询表头信息
            List<DeviceDetectionTableHeader> headers = dataService.getTableHeadersByDetectionDataId(detectionDataId);
            if (headers == null || headers.isEmpty()) {
                logger.warn("未找到表头信息，检测数据ID: {}", detectionDataId);
                return new ArrayList<>();
            }

            // 查询表格数据
            List<DeviceDetectionTableData> tableDataList = dataService.getTableDataByDetectionDataId(detectionDataId);
            List<TableDataInfo> result = new ArrayList<>();

            if (tableDataList != null) {
                for (DeviceDetectionTableData tableData : tableDataList) {
                    // 解析JSON格式的row_data
                    List<TableDataInfo> parsedRows = parseRowDataWithHeaders(tableData, headers);
                    result.addAll(parsedRows);
                }
            }

            logger.debug("加载表格数据成功，检测数据ID: {}, 数据行数: {}", detectionDataId, result.size());
            return result;

        } catch (Exception e) {
            logger.error("加载表格数据失败，检测数据ID: {}", detectionDataId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析行数据JSON，根据表头信息转换为TableDataInfo列表
     */
    private List<TableDataInfo> parseRowDataWithHeaders(DeviceDetectionTableData tableData,
                                                       List<DeviceDetectionTableHeader> headers) {
        List<TableDataInfo> result = new ArrayList<>();

        try {
            String rowDataJson = tableData.getRowData();
            if (rowDataJson == null || rowDataJson.trim().isEmpty()) {
                return result;
            }

            // 使用JsonParseUtil解析JSON
            Map<String, String> jsonData = JsonParseUtil.parseSimpleJson(rowDataJson);

            // 如果主解析失败，尝试备用解析方法
            if (jsonData.isEmpty() && JsonParseUtil.isValidJson(rowDataJson)) {
                jsonData = JsonParseUtil.parseSimpleJsonFallback(rowDataJson);
            }

            // 将解析后的数据转换为TableDataInfo
            for (Map.Entry<String, String> entry : jsonData.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                // 根据key找到对应的表头信息
                DeviceDetectionTableHeader matchedHeader = findHeaderByCode(headers, key);

                TableDataInfo tableDataInfo = new TableDataInfo();
                tableDataInfo.setSheetId(tableData.getSheetId());
                tableDataInfo.setSheetName(tableData.getSheetName());
                tableDataInfo.setRowIndex(tableData.getRowIndex());
                tableDataInfo.setFieldCode(key);
                tableDataInfo.setFieldValue(value);

                // 如果找到匹配的表头，使用表头的名称
                if (matchedHeader != null) {
                    tableDataInfo.setFieldName(matchedHeader.getHeaderName());
                } else {
                    tableDataInfo.setFieldName(key); // 使用code作为名称
                }

                result.add(tableDataInfo);
            }

            logger.debug("解析行数据成功，行索引: {}, 字段数: {}", tableData.getRowIndex(), result.size());

        } catch (Exception e) {
            logger.error("解析行数据JSON失败，行索引: {}, JSON: {}", tableData.getRowIndex(), tableData.getRowData(), e);
        }

        return result;
    }

    /**
     * 根据header_code查找对应的表头信息
     */
    private DeviceDetectionTableHeader findHeaderByCode(List<DeviceDetectionTableHeader> headers, String headerCode) {
        if (headers == null || headerCode == null) {
            return null;
        }

        for (DeviceDetectionTableHeader header : headers) {
            if (headerCode.equals(header.getHeaderCode())) {
                return header;
            }
        }

        return null;
    }

    /**
     * 获取文件大小
     */
    public static long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            return 0;
        }
    }


    /**
     * 获取MyBatis Plus配置实例
     */
    public MyBatisPlusConfig getMyBatisPlusConfig() {
        return myBatisPlusConfig;
    }

    // 内部类定义
    public static class DeviceDetectionDataDetail {
        private DeviceDetectionData mainRecord;
        private List<BasicFieldData> basicFields;
        private List<TableDataInfo> tableData;

        // Getters and Setters
        public DeviceDetectionData getMainRecord() {
            return mainRecord;
        }

        public void setMainRecord(DeviceDetectionData mainRecord) {
            this.mainRecord = mainRecord;
        }

        public List<BasicFieldData> getBasicFields() {
            return basicFields;
        }

        public void setBasicFields(List<BasicFieldData> basicFields) {
            this.basicFields = basicFields;
        }

        public List<TableDataInfo> getTableData() {
            return tableData;
        }

        public void setTableData(List<TableDataInfo> tableData) {
            this.tableData = tableData;
        }

    }

    /**
     * 基础字段数据类
     */
    public static class BasicFieldData {
        private String sheetId;
        private String sheetName;
        private String fieldCode;
        private String fieldName;
        private String fieldValue;
        private String fieldType;
        private Integer rowIndex;
        private Integer colIndex;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }

        public String getFieldType() { return fieldType; }
        public void setFieldType(String fieldType) { this.fieldType = fieldType; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public Integer getColIndex() { return colIndex; }
        public void setColIndex(Integer colIndex) { this.colIndex = colIndex; }
    }

    /**
     * 表格数据信息类
     */
    public static class TableDataInfo {
        private String sheetId;
        private String sheetName;
        private Integer rowIndex;
        private String fieldCode;
        private String fieldName;
        private String fieldValue;

        // Getters and Setters
        public String getSheetId() { return sheetId; }
        public void setSheetId(String sheetId) { this.sheetId = sheetId; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public Integer getRowIndex() { return rowIndex; }
        public void setRowIndex(Integer rowIndex) { this.rowIndex = rowIndex; }

        public String getFieldCode() { return fieldCode; }
        public void setFieldCode(String fieldCode) { this.fieldCode = fieldCode; }

        public String getFieldName() { return fieldName; }
        public void setFieldName(String fieldName) { this.fieldName = fieldName; }

        public String getFieldValue() { return fieldValue; }
        public void setFieldValue(String fieldValue) { this.fieldValue = fieldValue; }
    }
}
