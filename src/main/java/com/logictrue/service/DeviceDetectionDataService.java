package com.logictrue.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.config.MyBatisPlusConfig;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import com.logictrue.mapper.DeviceDetectionBasicFieldMapper;
import com.logictrue.mapper.DeviceDetectionDataMapper;
import com.logictrue.mapper.DeviceDetectionTableDataMapper;
import com.logictrue.mapper.DeviceDetectionTableHeaderMapper;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * 设备检测数据服务类
 * 使用MyBatis-Plus提供DeviceDetectionData在SQLite数据库中的新增和修改操作
 */
public class DeviceDetectionDataService {
    private static final Logger logger = LoggerFactory.getLogger(DeviceDetectionDataService.class);

    private final MyBatisPlusConfig myBatisPlusConfig;

    // 使用新的Service类来处理批量插入
    private final DeviceDetectionBasicFieldService basicFieldService;
    private final DeviceDetectionTableHeaderService tableHeaderService;
    private final DeviceDetectionTableDataService tableDataService;

    public DeviceDetectionDataService() {
        this.myBatisPlusConfig = MyBatisPlusConfig.getInstance();

        // 初始化Service实例
        this.basicFieldService = new DeviceDetectionBasicFieldService();
        this.tableHeaderService = new DeviceDetectionTableHeaderService();
        this.tableDataService = new DeviceDetectionTableDataService();
    }

    /**
     * 执行数据库操作，自动管理SqlSession生命周期
     */
    private <T> T executeWithSession(Function<SqlSession, T> operation) {
        SqlSession sqlSession = null;
        try {
            sqlSession = myBatisPlusConfig.getSqlSession();
            return operation.apply(sqlSession);
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }

    /**
     * 在解析前插入记录并获取ID
     *
     * @return 插入记录的ID，失败返回null
     */
    public Long insertBeforeParsing(DeviceDetectionData detectionData) {
        try {
            // 设置默认值
            detectionData.setParseStatus(0); // 0-待解析
            detectionData.setCreateTime(LocalDateTime.now());

            // 使用自动管理连接的方式插入
            int result = executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.insert(detectionData);
            });

            if (result > 0) {
                Long id = detectionData.getId();
                logger.info("成功插入设备检测数据记录，ID: {}, 文件: {}", id, detectionData.getFilePath());
                return id;
            }

        } catch (Exception e) {
            logger.error("插入设备检测数据记录失败: {}", detectionData.getFilePath(), e);
        }

        return null;
    }

    /**
     * 解析完成后更新状态
     *
     * @param id 记录ID
     * @param parseStatus 解析状态 (0-待解析，1-解析成功，2-解析失败)
     * @param parseMessage 解析消息
     * @param totalSheets 总工作表数
     * @param parsedSheets 已解析工作表数
     * @param basicFieldsCount 基础字段数量
     * @param tableRowsCount 表格行数
     * @param updateBy 更新人
     * @return 更新是否成功
     */
    public boolean updateAfterParsing(Long id, Integer parseStatus, String parseMessage,
                                     Integer totalSheets, Integer parsedSheets,
                                     Integer basicFieldsCount, Integer tableRowsCount,
                                     String updateBy) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 使用自动管理连接的方式更新
            int result = executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.updateAfterParsing(id, parseStatus, parseMessage, now,
                        totalSheets, parsedSheets, basicFieldsCount, tableRowsCount, updateBy, now);
            });

            if (result > 0) {
                logger.info("成功更新设备检测数据记录状态，ID: {}, 状态: {}", id, parseStatus);
                return true;
            } else {
                logger.warn("未找到要更新的记录，ID: {}", id);
                return false;
            }

        } catch (Exception e) {
            logger.error("更新设备检测数据记录状态失败，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 批量插入基础字段数据
     *
     * @param detectionDataId 检测数据ID
     * @param basicFields 基础字段列表
     * @return 插入是否成功
     */
    public boolean batchInsertBasicFields(Long detectionDataId, List<DeviceDetectionBasicField> basicFields) {
        // 委托给专门的Service处理
        return basicFieldService.batchInsertBasicFields(detectionDataId, basicFields);
    }


    /**
     * 添加查询方法
     */

    /**
     * 分页查询设备检测数据
     */
    public List<DeviceDetectionData> getPageData(int page, int pageSize) {
        try {
            int offset = (page - 1) * pageSize;
            return executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.selectPageData(offset, pageSize);
            });
        } catch (Exception e) {
            logger.error("分页查询设备检测数据失败，页码: {}, 页大小: {}", page, pageSize, e);
            return null;
        }
    }

    /**
     * 根据设备编码查询
     */
    public List<DeviceDetectionData> getByDeviceCode(String deviceCode) {
        try {
            return executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.selectByDeviceCode(deviceCode);
            });
        } catch (Exception e) {
            logger.error("根据设备编码查询失败，设备编码: {}", deviceCode, e);
            return null;
        }
    }

    /**
     * 根据解析状态查询
     */
    public List<DeviceDetectionData> getByParseStatus(Integer parseStatus) {
        try {
            return executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.selectByParseStatus(parseStatus);
            });
        } catch (Exception e) {
            logger.error("根据解析状态查询失败，状态: {}", parseStatus, e);
            return null;
        }
    }

    /**
     * 根据ID查询设备检测数据
     */
    public DeviceDetectionData getById(Long id) {
        try {
            return executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.selectById(id);
            });
        } catch (Exception e) {
            logger.error("根据ID查询设备检测数据失败，ID: {}", id, e);
            return null;
        }
    }

    /**
     * 根据检测数据ID查询基础字段数据
     */
    public List<DeviceDetectionBasicField> getBasicFieldsByDetectionDataId(Long detectionDataId) {
        try {
            return basicFieldService.getByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询基础字段失败，ID: {}", detectionDataId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据检测数据ID查询表格数据
     */
    public List<DeviceDetectionTableData> getTableDataByDetectionDataId(Long detectionDataId) {
        try {
            return tableDataService.getByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表格数据失败，ID: {}", detectionDataId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据检测数据ID查询表头数据
     */
    public List<DeviceDetectionTableHeader> getTableHeadersByDetectionDataId(Long detectionDataId) {
        try {
            return tableHeaderService.getByDetectionDataId(detectionDataId);
        } catch (Exception e) {
            logger.error("根据检测数据ID查询表头数据失败，ID: {}", detectionDataId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计总记录数
     */
    public long getTotalCount() {
        try {
            return executeWithSession(sqlSession -> {
                DeviceDetectionDataMapper mapper = sqlSession.getMapper(DeviceDetectionDataMapper.class);
                return mapper.countTotal();
            });
        } catch (Exception e) {
            logger.error("统计总记录数失败", e);
            return 0;
        }
    }

}
