package com.logictrue.service;

import com.alibaba.fastjson2.JSON;
import com.logictrue.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 网络服务类，处理HTTP请求和文件下载
 */
public class NetworkService {
    private static final Logger logger = LoggerFactory.getLogger(NetworkService.class);
    private static final String CACHE_DIR = System.getProperty("user.home") + "/.iot-jfx/cache";
    
    private HttpClient httpClient;
    private ConfigManager configManager;

    public NetworkService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(5))
                .build();
        this.configManager = ConfigManager.getInstance();

        // 创建缓存目录
        try {
            Files.createDirectories(Paths.get(CACHE_DIR));
        } catch (IOException e) {
            logger.error("创建缓存目录失败", e);
        }
    }
    
    /**
     * 异步下载设备图片
     */
    public CompletableFuture<String> downloadDeviceImage(String deviceId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String imageUrl = configManager.getImageUrl() + "?deviceId=" + deviceId;
                String fileName = "device_" + deviceId + ".jpg";
                String localPath = CACHE_DIR + File.separator + fileName;

                logger.info("开始下载设备图片: {}", imageUrl);

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(imageUrl))
                        .timeout(Duration.ofSeconds(30))
                        .GET()
                        .build();

                HttpResponse<InputStream> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofInputStream());

                if (response.statusCode() == 200) {
                    try (InputStream inputStream = response.body();
                         FileOutputStream outputStream = new FileOutputStream(localPath)) {

                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }

                    logger.info("设备图片下载成功: {}", localPath);
                    return localPath;
                } else {
                    logger.error("下载设备图片失败，HTTP状态码: {}", response.statusCode());
                    return null;
                }
            } catch (Exception e) {
                logger.error("下载设备图片异常", e);
                return null;
            }
        });
    }
    
    /**
     * 异步发送心跳请求
     */
    public CompletableFuture<HeartbeatResult> sendHeartbeat() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                String heartbeatUrl = configManager.getHeartbeatUrl();

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(heartbeatUrl))
                        .timeout(Duration.ofSeconds(5))
                        .GET()
                        .build();

                HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());
                long responseTime = System.currentTimeMillis() - startTime;

                int statusCode = response.statusCode();
                String responseBody = response.body();

                boolean success = statusCode >= 200 && statusCode < 300;

                return new HeartbeatResult(success, statusCode, responseTime, responseBody);
            } catch (Exception e) {
                long responseTime = System.currentTimeMillis() - startTime;
                logger.error("心跳请求异常", e);
                return new HeartbeatResult(false, -1, responseTime, e.getMessage());
            }
        });
    }
    
    /**
     * 异步提交表单数据
     */
    public CompletableFuture<Boolean> submitForm(Map<String, Object> formData) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String apiUrl = configManager.getApiUrl();
                String jsonData = JSON.toJSONString(formData);

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(apiUrl))
                        .timeout(Duration.ofSeconds(30))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                        .build();

                HttpResponse<String> response = httpClient.send(request,
                        HttpResponse.BodyHandlers.ofString());
                int statusCode = response.statusCode();

                boolean success = statusCode >= 200 && statusCode < 300;
                logger.info("表单提交结果: {}, 状态码: {}", success ? "成功" : "失败", statusCode);

                return success;
            } catch (Exception e) {
                logger.error("表单提交异常", e);
                return false;
            }
        });
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        // Java 11+ HttpClient 不需要显式关闭
        logger.info("NetworkService 已关闭");
    }
    
    /**
     * 心跳结果数据类
     */
    public static class HeartbeatResult {
        private final boolean success;
        private final int statusCode;
        private final long responseTime;
        private final String message;
        
        public HeartbeatResult(boolean success, int statusCode, long responseTime, String message) {
            this.success = success;
            this.statusCode = statusCode;
            this.responseTime = responseTime;
            this.message = message;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public int getStatusCode() {
            return statusCode;
        }
        
        public long getResponseTime() {
            return responseTime;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
