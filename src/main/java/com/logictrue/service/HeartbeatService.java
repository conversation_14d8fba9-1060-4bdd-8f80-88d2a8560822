package com.logictrue.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 心跳监控服务
 */
public class HeartbeatService {
    private static final Logger logger = LoggerFactory.getLogger(HeartbeatService.class);
    private static final int HEARTBEAT_INTERVAL = 30; // 30秒间隔
    
    private ScheduledExecutorService scheduler;
    private NetworkService networkService;
    private boolean running = false;
    
    public HeartbeatService() {
        this.networkService = new NetworkService();
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "HeartbeatService");
            thread.setDaemon(true);
            return thread;
        });
    }
    
    /**
     * 启动心跳监控
     */
    public void start() {
        if (running) {
            logger.warn("心跳服务已经在运行中");
            return;
        }
        
        running = true;
        logger.info("启动心跳监控服务，间隔: {}秒", HEARTBEAT_INTERVAL);
        
        scheduler.scheduleAtFixedRate(this::performHeartbeat, 0, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }
    
    /**
     * 停止心跳监控
     */
    public void stop() {
        if (!running) {
            return;
        }
        
        running = false;
        logger.info("停止心跳监控服务");
        
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 执行心跳检测
     */
    private void performHeartbeat() {
        try {
            networkService.sendHeartbeat().thenAccept(result -> {
                if (result.isSuccess()) {
                    logger.info("心跳检测成功 - 状态码: {}, 响应时间: {}ms", 
                            result.getStatusCode(), result.getResponseTime());
                } else {
                    logger.warn("心跳检测失败 - 状态码: {}, 响应时间: {}ms, 错误信息: {}", 
                            result.getStatusCode(), result.getResponseTime(), result.getMessage());
                }
            }).exceptionally(throwable -> {
                logger.error("心跳检测异常", throwable);
                return null;
            });
        } catch (Exception e) {
            logger.error("执行心跳检测时发生异常", e);
        }
    }
    
    /**
     * 检查服务是否正在运行
     */
    public boolean isRunning() {
        return running;
    }
    
    /**
     * 立即执行一次心跳检测
     */
    public void performImmediateHeartbeat() {
        if (networkService != null) {
            performHeartbeat();
        }
    }
}
