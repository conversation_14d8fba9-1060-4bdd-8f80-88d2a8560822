package com.logictrue.model;

/**
 * 表单字段数据模型
 */
public class FormField {

    /**
     * 字段类型枚举
     */
    public enum FieldType {
        TEXT("文本框"),
        NUMBER("数字框"),
        TEXTAREA("多行文本"),
        DATE("日期"),
        TIME("时间"),
        DATETIME("日期时间");

        private final String displayName;

        FieldType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    private String id;              // 字段唯一标识
    private String label;           // 字段显示标签
    private String name;            // 字段名称（用于数据提交）
    private FieldType type;         // 字段类型

    public FormField() {
        this.type = FieldType.TEXT;
    }

    public FormField(String id, String label, String name, FieldType type) {
        this();
        this.id = id;
        this.label = label;
        this.name = name;
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public FieldType getType() {
        return type;
    }

    public void setType(FieldType type) {
        this.type = type;
    }



    @Override
    public String toString() {
        return "FormField{" +
                "id='" + id + '\'' +
                ", label='" + label + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FormField formField = (FormField) o;
        return id != null ? id.equals(formField.id) : formField.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
