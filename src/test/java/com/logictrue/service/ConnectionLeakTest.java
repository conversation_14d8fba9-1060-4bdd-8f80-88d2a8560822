package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionBasicField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 连接泄漏测试类
 * 用于验证BatchInsertService是否存在连接泄漏问题
 */
public class ConnectionLeakTest {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionLeakTest.class);
    
    /**
     * 测试连续多次批量插入是否会导致连接泄漏
     */
    public static void testConnectionLeak() {
        logger.info("开始连接泄漏测试");
        
        BatchInsertService batchService = BatchInsertService.getInstance();
        
        // 执行多次批量插入操作
        for (int i = 0; i < 20; i++) {
            logger.info("执行第{}次批量插入测试", i + 1);
            
            // 创建测试数据
            List<DeviceDetectionBasicField> testData = createTestData(100);
            
            // 执行批量插入
            int result = batchService.batchInsertBasicFields(testData);
            
            logger.info("第{}次批量插入完成，结果: {}", i + 1, result);
            
            // 短暂等待，观察连接池状态
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("连接泄漏测试完成");
    }
    
    /**
     * 测试并发批量插入是否会导致连接泄漏
     */
    public static void testConcurrentConnectionLeak() {
        logger.info("开始并发连接泄漏测试");
        
        BatchInsertService batchService = BatchInsertService.getInstance();
        ExecutorService executor = Executors.newFixedThreadPool(5);
        
        List<Future<?>> futures = new ArrayList<>();
        
        // 启动多个并发任务
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            Future<?> future = executor.submit(() -> {
                try {
                    logger.info("并发任务{}开始", taskId);
                    
                    // 创建测试数据
                    List<DeviceDetectionBasicField> testData = createTestData(50);
                    
                    // 执行批量插入
                    int result = batchService.batchInsertBasicFields(testData);
                    
                    logger.info("并发任务{}完成，结果: {}", taskId, result);
                    
                } catch (Exception e) {
                    logger.error("并发任务{}执行失败", taskId, e);
                }
            });
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get(30, TimeUnit.SECONDS);
            } catch (Exception e) {
                logger.error("等待并发任务完成时出错", e);
            }
        }
        
        executor.shutdown();
        try {
            if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        logger.info("并发连接泄漏测试完成");
    }
    
    /**
     * 测试长时间运行是否会导致连接泄漏
     */
    public static void testLongRunningConnectionLeak() {
        logger.info("开始长时间运行连接泄漏测试");
        
        BatchInsertService batchService = BatchInsertService.getInstance();
        
        long startTime = System.currentTimeMillis();
        long testDuration = 5 * 60 * 1000; // 5分钟
        int operationCount = 0;
        
        while (System.currentTimeMillis() - startTime < testDuration) {
            operationCount++;
            
            // 创建小批量测试数据
            List<DeviceDetectionBasicField> testData = createTestData(10);
            
            // 执行批量插入
            int result = batchService.batchInsertBasicFields(testData);
            
            if (operationCount % 50 == 0) {
                logger.info("长时间测试进行中，已执行{}次操作", operationCount);
            }
            
            // 短暂等待
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("长时间运行连接泄漏测试完成，总共执行{}次操作", operationCount);
    }
    
    /**
     * 创建测试数据
     */
    private static List<DeviceDetectionBasicField> createTestData(int count) {
        List<DeviceDetectionBasicField> fields = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < count; i++) {
            DeviceDetectionBasicField field = new DeviceDetectionBasicField();
            field.setDetectionDataId(1L);
            field.setSheetId("test_sheet");
            field.setSheetName("测试工作表");
            field.setSheetIndex(0);
            field.setFieldCode("test_field_" + i);
            field.setFieldName("测试字段_" + i);
            field.setFieldType("TEXT");
            field.setFieldValue("测试值_" + i);
            field.setLabelPosition("A" + (i + 1));
            field.setValuePosition("B" + (i + 1));
            field.setLabelRowIndex(i + 1);
            field.setLabelColIndex(0);
            field.setValueRowIndex(i + 1);
            field.setValueColIndex(1);
            field.setSortOrder(i);
            field.setCreateTime(now);
            
            fields.add(field);
        }
        
        return fields;
    }
    
    /**
     * 主方法，运行所有连接泄漏测试
     */
    public static void main(String[] args) {
        logger.info("=== 开始连接泄漏测试套件 ===");
        
        try {
            // 测试1：连续批量插入
            testConnectionLeak();
            
            // 等待一段时间，观察连接池状态
            Thread.sleep(5000);
            
            // 测试2：并发批量插入
            testConcurrentConnectionLeak();
            
            // 等待一段时间，观察连接池状态
            Thread.sleep(5000);
            
            // 测试3：长时间运行（可选，时间较长）
            // testLongRunningConnectionLeak();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("测试被中断", e);
        }
        
        logger.info("=== 连接泄漏测试套件完成 ===");
        logger.info("请观察日志中是否还有连接泄漏警告");
    }
}
