package com.logictrue.service;

import com.logictrue.iot.entity.DeviceDetectionBasicField;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.entity.DeviceDetectionTableHeader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量插入服务测试类
 */
public class BatchInsertServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(BatchInsertServiceTest.class);
    
    private BatchInsertService batchInsertService;
    
    @BeforeEach
    public void setUp() {
        batchInsertService = BatchInsertService.getInstance();
    }
    
    /**
     * 测试大量基础字段数据的批量插入
     */
    @Test
    public void testBatchInsertLargeBasicFields() {
        logger.info("开始测试大量基础字段数据的批量插入");
        
        // 创建大量测试数据（模拟实际场景中的大数据量）
        List<DeviceDetectionBasicField> basicFields = createTestBasicFields(2000);
        
        long startTime = System.currentTimeMillis();
        int result = batchInsertService.batchInsertBasicFields(basicFields);
        long endTime = System.currentTimeMillis();
        
        logger.info("批量插入基础字段完成，结果: {}, 耗时: {}ms", result, endTime - startTime);
        
        // 验证结果
        if (result > 0) {
            logger.info("✓ 批量插入基础字段成功");
        } else {
            logger.error("✗ 批量插入基础字段失败");
        }
    }
    
    /**
     * 测试大量表格数据的批量插入
     */
    @Test
    public void testBatchInsertLargeTableData() {
        logger.info("开始测试大量表格数据的批量插入");
        
        // 创建大量测试数据
        List<DeviceDetectionTableData> tableDataList = createTestTableData(3000);
        
        long startTime = System.currentTimeMillis();
        int result = batchInsertService.batchInsertTableData(tableDataList);
        long endTime = System.currentTimeMillis();
        
        logger.info("批量插入表格数据完成，结果: {}, 耗时: {}ms", result, endTime - startTime);
        
        // 验证结果
        if (result > 0) {
            logger.info("✓ 批量插入表格数据成功");
        } else {
            logger.error("✗ 批量插入表格数据失败");
        }
    }
    
    /**
     * 测试混合数据的批量插入
     */
    @Test
    public void testBatchInsertAll() {
        logger.info("开始测试混合数据的批量插入");
        
        // 创建测试数据
        List<DeviceDetectionBasicField> basicFields = createTestBasicFields(1000);
        List<DeviceDetectionTableHeader> tableHeaders = createTestTableHeaders(50);
        List<DeviceDetectionTableData> tableDataList = createTestTableData(1500);
        
        long startTime = System.currentTimeMillis();
        BatchInsertService.BatchInsertResult result = batchInsertService.batchInsertAll(
            basicFields, tableHeaders, tableDataList);
        long endTime = System.currentTimeMillis();
        
        logger.info("批量插入所有数据完成，结果: {}, 耗时: {}ms", result.isSuccess(), endTime - startTime);
        logger.info("插入统计 - 基础字段: {}, 表头: {}, 表格数据: {}, 总计: {}", 
                   result.getBasicFieldCount(), result.getTableHeaderCount(), 
                   result.getTableDataCount(), result.getTotalCount());
        
        // 验证结果
        if (result.isSuccess()) {
            logger.info("✓ 批量插入所有数据成功");
        } else {
            logger.error("✗ 批量插入所有数据失败");
        }
    }
    
    /**
     * 创建测试基础字段数据
     */
    private List<DeviceDetectionBasicField> createTestBasicFields(int count) {
        List<DeviceDetectionBasicField> fields = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < count; i++) {
            DeviceDetectionBasicField field = new DeviceDetectionBasicField();
            field.setDetectionDataId(1L);
            field.setSheetId("sheet_" + (i % 5));
            field.setSheetName("测试工作表_" + (i % 5));
            field.setSheetIndex(i % 5);
            field.setFieldCode("field_" + i);
            field.setFieldName("测试字段_" + i);
            field.setFieldType("TEXT");
            field.setFieldValue("测试值_" + i);
            field.setLabelPosition("A" + (i + 1));
            field.setValuePosition("B" + (i + 1));
            field.setLabelRowIndex(i + 1);
            field.setLabelColIndex(0);
            field.setValueRowIndex(i + 1);
            field.setValueColIndex(1);
            field.setSortOrder(i);
            field.setCreateTime(now);
            
            fields.add(field);
        }
        
        return fields;
    }
    
    /**
     * 创建测试表头数据
     */
    private List<DeviceDetectionTableHeader> createTestTableHeaders(int count) {
        List<DeviceDetectionTableHeader> headers = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < count; i++) {
            DeviceDetectionTableHeader header = new DeviceDetectionTableHeader();
            header.setDetectionDataId(1L);
            header.setSheetId("sheet_table");
            header.setSheetName("测试表格工作表");
            header.setSheetIndex(0);
            header.setHeaderName("表头_" + i);
            header.setHeaderCode("header_" + i);
            header.setHeaderPosition(String.valueOf((char)('A' + i)) + "1");
            header.setHeaderRowIndex(1);
            header.setHeaderColIndex(i);
            header.setDataType("TEXT");
            header.setColumnOrder(i);
            header.setCreateTime(now);
            
            headers.add(header);
        }
        
        return headers;
    }
    
    /**
     * 创建测试表格数据
     */
    private List<DeviceDetectionTableData> createTestTableData(int count) {
        List<DeviceDetectionTableData> dataList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (int i = 0; i < count; i++) {
            DeviceDetectionTableData data = new DeviceDetectionTableData();
            data.setDetectionDataId(1L);
            data.setSheetId("sheet_table");
            data.setSheetName("测试表格工作表");
            data.setSheetIndex(0);
            data.setRowIndex(i + 2); // 从第2行开始（第1行是表头）
            data.setRowData("{\"col1\":\"数据" + i + "_1\",\"col2\":\"数据" + i + "_2\",\"col3\":\"数据" + i + "_3\"}");
            data.setRowOrder(i);
            data.setCreateTime(now);
            
            dataList.add(data);
        }
        
        return dataList;
    }
    
    /**
     * 主方法，用于直接运行测试
     */
    public static void main(String[] args) {
        BatchInsertServiceTest test = new BatchInsertServiceTest();
        test.setUp();
        
        logger.info("=== 开始批量插入性能测试 ===");
        
        // 运行测试
        test.testBatchInsertLargeBasicFields();
        test.testBatchInsertLargeTableData();
        test.testBatchInsertAll();
        
        logger.info("=== 批量插入性能测试完成 ===");
    }
}
