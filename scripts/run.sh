#!/bin/bash

# IoT JavaFX 应用启动脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 设置 Java 路径
JAVA_HOME="${JAVA_HOME:-/home/<USER>/Java/jdk-21.0.8+9}"
JAVA_CMD="$JAVA_HOME/bin/java"

# 检查 Java 是否存在
if [ ! -f "$JAVA_CMD" ]; then
    echo "错误: 找不到 Java 可执行文件: $JAVA_CMD"
    echo "请设置正确的 JAVA_HOME 环境变量"
    exit 1
fi

# 设置类路径
JAR_FILE="$PROJECT_DIR/target/iot-jfx-1.0-SNAPSHOT.jar"
LIBS_DIR="$PROJECT_DIR/target/libs"

if [ ! -f "$JAR_FILE" ]; then
    echo "错误: 找不到 JAR 文件: $JAR_FILE"
    echo "请先运行: mvn clean package"
    exit 1
fi

# 构建类路径
CLASSPATH="$JAR_FILE"
if [ -d "$LIBS_DIR" ]; then
    for jar in "$LIBS_DIR"/*.jar; do
        if [ -f "$jar" ]; then
            CLASSPATH="$CLASSPATH:$jar"
        fi
    done
fi

# JavaFX 模块路径
JAVAFX_MODULES="javafx.controls,javafx.fxml"

# 启动应用
echo "启动 IoT JavaFX 应用..."
echo "Java 版本: $($JAVA_CMD -version 2>&1 | head -n 1)"
echo "JAR 文件: $JAR_FILE"

"$JAVA_CMD" \
    --module-path "$CLASSPATH" \
    --add-modules "$JAVAFX_MODULES" \
    -Dfile.encoding=UTF-8 \
    -Djava.awt.headless=false \
    -cp "$CLASSPATH" \
    com.logictrue.App "$@"
