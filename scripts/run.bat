@echo off
REM IoT JavaFX 应用启动脚本 (Windows)

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

REM 设置 Java 路径
if "%JAVA_HOME%"=="" (
    set JAVA_HOME=C:\Program Files\Java\jdk-21
)
set JAVA_CMD=%JAVA_HOME%\bin\java.exe

REM 检查 Java 是否存在
if not exist "%JAVA_CMD%" (
    echo 错误: 找不到 Java 可执行文件: %JAVA_CMD%
    echo 请设置正确的 JAVA_HOME 环境变量
    pause
    exit /b 1
)

REM 设置类路径
set JAR_FILE=%PROJECT_DIR%\target\iot-jfx-1.0-SNAPSHOT.jar
set LIBS_DIR=%PROJECT_DIR%\target\libs

if not exist "%JAR_FILE%" (
    echo 错误: 找不到 JAR 文件: %JAR_FILE%
    echo 请先运行: mvn clean package
    pause
    exit /b 1
)

REM 构建类路径
set CLASSPATH=%JAR_FILE%
if exist "%LIBS_DIR%" (
    for %%f in ("%LIBS_DIR%\*.jar") do (
        set CLASSPATH=!CLASSPATH!;%%f
    )
)

REM JavaFX 模块路径
set JAVAFX_MODULES=javafx.controls,javafx.fxml

REM 启动应用
echo 启动 IoT JavaFX 应用...
echo JAR 文件: %JAR_FILE%

"%JAVA_CMD%" ^
    --module-path "%CLASSPATH%" ^
    --add-modules "%JAVAFX_MODULES%" ^
    -Dfile.encoding=UTF-8 ^
    -Djava.awt.headless=false ^
    -cp "%CLASSPATH%" ^
    com.logictrue.App %*

pause
