2025-08-05 16:11:49.031 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 16:11:49.314 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (jar模式): /home/<USER>/nl-mes/iot-jfx/target
2025-08-05 16:11:49.490 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:11:49.490 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 创建默认配置文件
2025-08-05 16:11:49.805 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:955)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:108)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1065)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-08-05 16:11:49.844 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-05 16:11:49.845 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共0个应用
2025-08-05 16:11:49.845 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 16:11:50.237 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 16:11:53.854 [JavaFX Application Thread] INFO  c.l.controller.MainController - 设置按钮Action事件被触发
2025-08-05 16:11:53.854 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始打开设置窗口
2025-08-05 16:11:53.974 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段表格初始化完成
2025-08-05 16:11:53.976 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 外部应用程序表格初始化完成
2025-08-05 16:11:53.977 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 配置文件管理初始化完成
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共6个
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载表单字段，共6个字段
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=deviceId, label=设备编号, name=deviceId, type=TEXT
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=operator, label=操作员, name=operator, type=TEXT
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=temperature, label=温度(°C), name=temperature, type=NUMBER
2025-08-05 16:11:53.978 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=humidity, label=湿度(%), name=humidity, type=NUMBER
2025-08-05 16:11:53.979 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=pressure, label=压力(Pa), name=pressure, type=NUMBER
2025-08-05 16:11:53.979 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=remarks, label=备注, name=remarks, type=TEXTAREA
2025-08-05 16:11:53.979 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段列表更新完成，当前列表大小: 6
2025-08-05 16:11:53.980 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载外部应用程序，共0个应用
2025-08-05 16:11:53.982 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试表单字段数据 ===
2025-08-05 16:11:53.983 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - formFieldsList大小: 6
2025-08-05 16:11:53.983 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[0]: id=deviceId, label=设备编号, name=deviceId, type=文本框
2025-08-05 16:11:53.984 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[1]: id=operator, label=操作员, name=operator, type=文本框
2025-08-05 16:11:53.984 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[2]: id=temperature, label=温度(°C), name=temperature, type=数字框
2025-08-05 16:11:53.984 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[3]: id=humidity, label=湿度(%), name=humidity, type=数字框
2025-08-05 16:11:53.984 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[4]: id=pressure, label=压力(Pa), name=pressure, type=数字框
2025-08-05 16:11:53.984 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[5]: id=remarks, label=备注, name=remarks, type=多行文本
2025-08-05 16:11:53.985 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试完成 ===
2025-08-05 16:11:53.985 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 16:11:54.392 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表格刷新完成
2025-08-05 16:12:05.847 [JavaFX Application Thread] INFO  c.l.controller.ExternalAppEditDialog - 外部应用程序保存成功: ExternalApp{id='fb25b5d8-8a00-428e-b16a-136101fc2b9b', name='111', path='/opt/AppImage/subtwo-2.20-x86_64.AppImage', arguments='null', workingDir='null', description='null'}
2025-08-05 16:12:05.865 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 新增外部应用程序: ExternalApp{id='fb25b5d8-8a00-428e-b16a-136101fc2b9b', name='111', path='/opt/AppImage/subtwo-2.20-x86_64.AppImage', arguments='null', workingDir='null', description='null'}
2025-08-05 16:12:19.720 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:955)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:108)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1065)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-08-05 16:12:20.450 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.451 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.452 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.453 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.454 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.454 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 保存表单字段配置，共5个字段
2025-08-05 16:12:20.455 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.455 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 保存外部应用程序配置，共1个应用
2025-08-05 16:12:20.458 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件保存成功: /home/<USER>/nl-mes/iot-jfx/target/config.json
2025-08-05 16:12:20.459 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置保存成功
2025-08-05 16:12:20.460 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-05 16:12:28.451 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 16:12:28.452 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-05 16:12:34.140 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-05 16:12:34.409 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (jar模式): /home/<USER>/nl-mes/iot-jfx/target
2025-08-05 16:12:34.586 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-05 16:12:34.908 [ForkJoinPool.commonPool-worker-1] ERROR com.logictrue.service.NetworkService - 心跳请求异常
java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.HttpClientImpl.send(HttpClientImpl.java:955)
	at java.net.http/jdk.internal.net.http.HttpClientFacade.send(HttpClientFacade.java:133)
	at com.logictrue.service.NetworkService.lambda$sendHeartbeat$1(NetworkService.java:108)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:387)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1312)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1843)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1808)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:188)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1065)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:227)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:280)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$2(PlainHttpConnection.java:238)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:202)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:786)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:874)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:210)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:212)
	... 9 common frames omitted
2025-08-05 16:12:34.931 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-05 16:12:34.932 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-05 16:12:34.933 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-05 16:12:35.320 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-05 16:12:36.896 [JavaFX Application Thread] INFO  c.l.controller.MainController - 设置按钮Action事件被触发
2025-08-05 16:12:36.897 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始打开设置窗口
2025-08-05 16:12:36.990 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段表格初始化完成
2025-08-05 16:12:36.991 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 外部应用程序表格初始化完成
2025-08-05 16:12:36.992 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 配置文件管理初始化完成
2025-08-05 16:12:36.993 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共5个
2025-08-05 16:12:36.993 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载表单字段，共5个字段
2025-08-05 16:12:36.993 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=deviceId, label=设备编号, name=deviceId, type=TEXT
2025-08-05 16:12:36.993 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=operator, label=操作员, name=operator, type=TEXT
2025-08-05 16:12:36.994 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=temperature, label=温度(°C), name=temperature, type=NUMBER
2025-08-05 16:12:36.994 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=humidity, label=湿度(%), name=humidity, type=NUMBER
2025-08-05 16:12:36.994 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=remarks, label=备注, name=remarks, type=TEXTAREA
2025-08-05 16:12:36.994 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段列表更新完成，当前列表大小: 5
2025-08-05 16:12:36.995 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载外部应用程序，共1个应用
2025-08-05 16:12:37.001 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试表单字段数据 ===
2025-08-05 16:12:37.002 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - formFieldsList大小: 5
2025-08-05 16:12:37.002 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[0]: id=deviceId, label=设备编号, name=deviceId, type=文本框
2025-08-05 16:12:37.002 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[1]: id=operator, label=操作员, name=operator, type=文本框
2025-08-05 16:12:37.002 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[2]: id=temperature, label=温度(°C), name=temperature, type=数字框
2025-08-05 16:12:37.002 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[3]: id=humidity, label=湿度(%), name=humidity, type=数字框
2025-08-05 16:12:37.003 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[4]: id=remarks, label=备注, name=remarks, type=多行文本
2025-08-05 16:12:37.003 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试完成 ===
2025-08-05 16:12:37.003 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-05 16:12:37.413 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表格刷新完成
2025-08-05 16:12:39.706 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-05 16:12:39.707 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-05 16:12:39.707 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共5个
2025-08-05 16:12:41.641 [JavaFX Application Thread] INFO  c.l.controller.MainController - 启动外部应用程序: 111
2025-08-05 16:12:41.642 [ForkJoinPool.commonPool-worker-1] INFO  c.l.service.ExternalAppService - 启动外部应用程序: 111
2025-08-05 16:12:41.650 [ForkJoinPool.commonPool-worker-1] INFO  c.l.service.ExternalAppService - 外部应用程序启动成功: 111 (PID: 105933)
2025-08-05 16:12:41.651 [JavaFX Application Thread] INFO  c.l.controller.MainController - 外部应用程序启动成功: 111
2025-08-05 16:12:45.624 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-05 16:12:45.625 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-05 16:12:48.351 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-05 16:12:48.353 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
